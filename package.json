{"name": "exp-social-media-content-generator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^1.0.15", "@langchain/core": "^0.3.64", "@langchain/mistralai": "^0.2.1", "@prisma/client": "^5.7.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "env-var": "^7.5.0", "langchain": "^0.3.30", "lucide-react": "^0.525.0", "next": "15.4.1", "next-auth": "^4.24.5", "prisma": "^5.7.1", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "packageManager": "pnpm@9.13.0+sha512.beb9e2a803db336c10c9af682b58ad7181ca0fbd0d4119f2b33d5f2582e96d6c0d93c85b23869295b765170fbdaa92890c0da6ada457415039769edf3c959efe"}