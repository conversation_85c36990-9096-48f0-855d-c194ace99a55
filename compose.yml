
services:
  postgresql:
    image: bitnami/postgresql:16.3.0
    restart: always
    environment:
      POSTGRESQL_USERNAME: ${POSTGRESQL_USERNAME:-postgres}
      POSTGRESQL_PASSWORD: ${POSTGRESQL_PASSWORD:-postgres}
      POSTGRESQL_DATABASE: ${POSTGRESQL_DATABASE:-postgres}
    ports:
      - '${POSTGRESQL_PUBLIC_PORT:-5432}:5432'
    volumes:
      - postgresql_data:/bitnami/postgresql

volumes:
  postgresql_data:
    driver: local