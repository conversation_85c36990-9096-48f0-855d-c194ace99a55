import { z } from 'zod';

export const WritingStyleSchema = z.object({
  tone: z.object({
    primary: z.string(),
    secondary: z.string(),
    emotional_range: z.array(z.string()),
    formality_level: z.enum([
      'very_formal',
      'formal',
      'neutral',
      'informal',
      'very_informal',
    ]),
  }),
  vocabulary: z.object({
    complexity_level: z.enum(['basic', 'intermediate', 'advanced', 'expert']),
    common_words: z.array(z.string()),
    technical_terms: z.array(z.string()),
    slang_colloquialisms: z.array(z.string()),
    preferred_adjectives: z.array(z.string()),
    preferred_verbs: z.array(z.string()),
  }),
  sentence_structure: z.object({
    average_length: z.enum([
      'very_short',
      'short',
      'medium',
      'long',
      'very_long',
    ]),
    complexity: z.enum(['simple', 'compound', 'complex', 'compound_complex']),
    preferred_starters: z.array(z.string()),
    punctuation_style: z.array(z.string()),
    question_frequency: z.enum([
      'never',
      'rarely',
      'sometimes',
      'often',
      'frequently',
    ]),
  }),
  paragraph_structure: z.object({
    typical_length: z.enum(['single_sentence', 'short', 'medium', 'long']),
    opening_style: z.array(z.string()),
    closing_style: z.array(z.string()),
    flow_patterns: z.array(z.string()),
  }),
  engagement_patterns: z.object({
    call_to_action_style: z.array(z.string()),
    hashtag_usage: z.enum(['none', 'minimal', 'moderate', 'heavy']),
    emoji_usage: z.enum(['none', 'minimal', 'moderate', 'heavy']),
    personal_pronouns: z.array(z.string()),
    audience_addressing: z.enum(['direct', 'indirect', 'mixed']),
  }),
  content_patterns: z.object({
    storytelling_style: z.enum([
      'narrative',
      'descriptive',
      'analytical',
      'persuasive',
      'mixed',
    ]),
    topic_introduction: z.array(z.string()),
    examples_usage: z.enum(['none', 'minimal', 'moderate', 'frequent']),
    personal_anecdotes: z.enum(['none', 'minimal', 'moderate', 'frequent']),
    humor_style: z.enum(['none', 'subtle', 'obvious', 'sarcastic', 'playful']),
  }),
  linguistic_features: z.object({
    repetition_patterns: z.array(z.string()),
    transition_words: z.array(z.string()),
    emphasis_techniques: z.array(z.string()),
    rhetorical_devices: z.array(z.string()),
    cultural_references: z.array(z.string()),
  }),
});

export const GenerationGuidelinesSchema = z.object({
  template_structure: z.string(),
  key_principles: z.array(z.string()),
  do_list: z.array(z.string()),
  dont_list: z.array(z.string()),
  adaptation_notes: z.array(z.string()),
});

export const ExamplesSchema = z.object({
  representative_excerpts: z.array(z.string()),
  style_variations: z.array(z.string()),
});

export const MetadataSchema = z.object({
  posts_analyzed: z.number(),
  total_word_count: z.number(),
  average_post_length: z.number(),
  confidence_score: z.number(),
});

export const StyleNotesSchema = z.object({
  tone_summary: z.string(), // Narrative description of tone beyond primary/secondary
  stylistic_signatures: z.array(z.string()), // Unique quirks like "uses rhetorical questions", "double emoji for emphasis"
  target_audience: z.string(), // e.g., "Friends and family", "Tech-savvy professionals"
  platform_behavior: z.string(), // e.g., "Engages often in comments", "Post-and-go style"
  overall_impression: z.string(),
});

export const StyleAnalysisSchema = z.object({
  writingStyle: WritingStyleSchema,
  generationGuidelines: GenerationGuidelinesSchema,
  examples: ExamplesSchema,
  metadata: MetadataSchema,
  styleNotes: StyleNotesSchema,
  style_notes: z.array(z.string()),
});

export const FacebookPostSchema = z.object({
  post_content: z.string(),
  hashtags: z.array(z.string()),
  engagement_hooks: z.array(z.string()),
  call_to_action: z.string().optional(),
  post_type: z.enum([
    'story',
    'opinion',
    'question',
    'announcement',
    'humor',
    'educational',
  ]),
  estimated_engagement: z.enum(['low', 'medium', 'high', 'viral']),
  best_posting_time: z.string(),
  target_audience_alignment: z.number().min(0).max(1),
  cultural_relevance: z.number().min(0).max(1),
  style_adherence: z.number().min(0).max(1),
  alternative_versions: z
    .array(
      z.object({
        content: z.string(),
        variation_type: z.string(),
        tone_adjustment: z.string(),
      })
    )
    .optional(),
});

export const TrendingTopicSchema = z.object({
  title: z.string(),
  description: z.string(),
  category: z.enum([
    'technology',
    'entertainment',
    'politics',
    'sports',
    'lifestyle',
    'education',
    'business',
    'culture',
    'health',
    'social_issues',
    'comedy',
    'personal_development',
    'other',
  ]),
  relevance_score: z.number().min(0).max(1),
  hashtags: z.array(z.string()),
  target_audience: z.array(z.string()),
  engagement_potential: z.enum(['low', 'medium', 'high', 'viral']),
  content_angle: z.string(),
  post_ideas: z.array(z.string()),
  timing_suggestion: z.string(),
  cultural_context: z.string().optional(),
});

export const TrendingTopicsAnalysisSchema = z.object({
  trending_topics: z.array(TrendingTopicSchema),
});
