import {
  WritingStyle,
  TrendingTopic,
  UserPreferences,
  GeneratedPost,
} from './storage';

export class ApiClient {
  private static baseUrl =
    process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3000';

  static async analyzeWritingStyle(facebookId: string): Promise<WritingStyle> {
    const response = await fetch(`${this.baseUrl}/api/analyze-writing-style`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ facebookId }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to analyze writing style: ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.writingStyle;
  }

  static async generateTrendingTopics(
    competitorIds: string[],
    userPreferences: UserPreferences
  ): Promise<TrendingTopic[]> {
    const response = await fetch(
      `${this.baseUrl}/api/generate-trending-topics`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ competitorIds, userPreferences }),
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to generate trending topics: ${response.statusText}`
      );
    }

    const data = await response.json();
    return data.trending_topics;
  }

  static async generatePost(
    topic: TrendingTopic,
    writingStyle: WritingStyle,
    options: {
      postType?: string;
      lengthPreference?: string;
      includeHashtags?: boolean;
      includeCallToAction?: boolean;
      generateVariations?: boolean;
      customInstructions?: string;
    } = {}
  ): Promise<GeneratedPost> {
    const response = await fetch(`${this.baseUrl}/api/generate-post`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ topic, writingStyle, options }),
    });

    if (!response.ok) {
      throw new Error(`Failed to generate post: ${response.statusText}`);
    }

    return await response.json();
  }
}
