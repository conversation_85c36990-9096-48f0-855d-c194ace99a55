/* eslint-disable @typescript-eslint/no-explicit-any */
'use server';

import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { DatabaseService } from '@/lib/database';
import { GeneratedPost } from '@/lib/storage';
import { Prisma, UserPreferences, WritingStyle } from '@prisma/client';

export async function saveWritingStyleAction(
  writingStyle: Prisma.WritingStyleCreateInput | Prisma.WritingStyleUpdateInput
) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  await DatabaseService.saveWritingStyle(session.user.id, writingStyle);
}

export async function getWritingStyleAction(): Promise<WritingStyle | null> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return null;
  }

  return await DatabaseService.getWritingStyle(session.user.id);
}

export async function saveTrendingTopicsAction(
  topics: Prisma.TrendingTopicCreateManyInput[]
) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  await DatabaseService.saveTrendingTopics(session.user.id, topics);
}

export async function getTrendingTopicsAction(): Promise<any[]> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return [];
  }

  return await DatabaseService.getTrendingTopics(session.user.id);
}

export async function saveUserPreferencesAction(preferences: any) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  await DatabaseService.saveUserPreferences(session.user.id, preferences);
}

export async function getUserPreferencesAction(): Promise<UserPreferences | null> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return null;
  }

  return await DatabaseService.getUserPreferences(session.user.id);
}

export async function saveCompetitorIdsAction(competitorIds: string[]) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  await DatabaseService.saveCompetitorIds(session.user.id, competitorIds);
}

export async function getCompetitorIdsAction(): Promise<string[]> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return [];
  }

  return await DatabaseService.getCompetitorIds(session.user.id);
}

export async function saveGeneratedPostAction(post: GeneratedPost) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  await DatabaseService.saveGeneratedPost(session.user.id, post);
}

export async function getGeneratedPostsAction(): Promise<GeneratedPost[]> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    return [];
  }

  return await DatabaseService.getGeneratedPosts(session.user.id);
}
