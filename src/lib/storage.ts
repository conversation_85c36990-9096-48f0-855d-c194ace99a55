import z from 'zod';
import { StyleAnalysisSchema } from './zod';

export type WritingStyle = z.infer<typeof StyleAnalysisSchema>;

export interface TrendingTopic {
  title: string;
  description: string;
  category: string;
  relevance_score: number;
  hashtags: string[];
  target_audience: string[];
  engagement_potential: string;
  content_angle: string;
  post_ideas: string[];
  timing_suggestion: string;
  cultural_context?: string;
}

export interface UserPreferences {
  interests: string[];
  target_audience: string[];
  content_style: string;
  language_preference: string;
  engagement_goals: string;
  avoid_topics: string[];
  preferred_categories: string[];
  tone_preference: string;
}

export interface GeneratedPost {
  post_content: string;
  hashtags: string[];
  engagement_hooks: string[];
  call_to_action?: string;
  post_type: string;
  estimated_engagement: string;
  best_posting_time: string;
  alternative_versions?: Array<{
    content: string;
    variation_type: string;
    tone_adjustment: string;
  }>;
}

export class LocalStorage {
  private static getItem<T>(key: string): T | null {
    if (typeof window === 'undefined') return null;
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error reading from localStorage key "${key}":`, error);
      return null;
    }
  }

  private static setItem<T>(key: string, value: T): void {
    if (typeof window === 'undefined') return;
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error writing to localStorage key "${key}":`, error);
    }
  }

  static getWritingStyle(): WritingStyle | null {
    return this.getItem<WritingStyle>('writingStyle');
  }

  static setWritingStyle(style: WritingStyle): void {
    this.setItem('writingStyle', style);
  }

  static getTrendingTopics(): TrendingTopic[] {
    return this.getItem<TrendingTopic[]>('trendingTopics') || [];
  }

  static setTrendingTopics(topics: TrendingTopic[]): void {
    this.setItem('trendingTopics', topics);
  }

  static getUserPreferences(): UserPreferences | null {
    return this.getItem<UserPreferences>('userPreferences');
  }

  static setUserPreferences(preferences: UserPreferences): void {
    this.setItem('userPreferences', preferences);
  }

  static getGeneratedPosts(): GeneratedPost[] {
    return this.getItem<GeneratedPost[]>('generatedPosts') || [];
  }

  static setGeneratedPosts(posts: GeneratedPost[]): void {
    this.setItem('generatedPosts', posts);
  }

  static getCompetitorIds(): string[] {
    return this.getItem<string[]>('competitorIds') || [];
  }

  static setCompetitorIds(ids: string[]): void {
    this.setItem('competitorIds', ids);
  }

  static getUserProfileId(): string | null {
    return this.getItem<string>('userProfileId');
  }

  static setUserProfileId(id: string): void {
    this.setItem('userProfileId', id);
  }
}
