/* eslint-disable @typescript-eslint/no-explicit-any */
import { Prisma, UserPreferences, WritingStyle } from '@prisma/client';
import { prisma } from './prisma';
import { GeneratedPost } from './storage';

export class DatabaseService {
  static async saveWritingStyle(
    userId: string,
    style: Prisma.WritingStyleCreateInput | Prisma.WritingStyleUpdateInput
  ): Promise<void> {
    await prisma.writingStyle.upsert({
      where: { userId },
      update: { ...style },
      create: {
        userId,
        data: style.data as any,
        facebookUserId: style.facebookUserId as string | undefined,
      },
    });
  }

  static async getWritingStyle(userId: string): Promise<WritingStyle | null> {
    const result = await prisma.writingStyle.findUnique({
      where: { userId },
    });
    return result;
  }

  static async saveTrendingTopics(
    userId: string,
    topics: Prisma.TrendingTopicCreateManyInput[]
  ): Promise<void> {
    await prisma.trendingTopic.createMany({
      data: topics,
    });
  }

  static async getTrendingTopics(userId: string): Promise<any[]> {
    const results = await prisma.trendingTopic.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    return results.map((r) => r.data);
  }

  static async saveGeneratedPost(
    userId: string,
    post: GeneratedPost
  ): Promise<void> {
    await prisma.generatedPost.create({
      data: {
        userId,
        content: post.post_content,
        data: post as any,
      },
    });
  }

  static async getGeneratedPosts(userId: string): Promise<GeneratedPost[]> {
    const results = await prisma.generatedPost.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
    return results.map((r) => r.data as any);
  }

  static async saveUserPreferences(
    userId: string,
    preferences: any
  ): Promise<void> {
    await prisma.userPreferences.upsert({
      where: { userId },
      update: { data: preferences as any },
      create: { userId, data: preferences as any },
    });
  }

  static async getUserPreferences(
    userId: string
  ): Promise<UserPreferences | null> {
    const result = await prisma.userPreferences.findUnique({
      where: { userId },
    });
    return result;
  }

  static async saveCompetitorIds(
    userId: string,
    competitorIds: string[]
  ): Promise<void> {
    await prisma.user.update({
      where: { id: userId },
      data: { competitorIds },
    });
  }

  static async getCompetitorIds(userId: string): Promise<string[]> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { competitorIds: true },
    });

    return user?.competitorIds || [];
  }
}
