import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import z from 'zod';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function parseJSONResponse(
  response: string,
  schema: z.ZodType,
  stepName: string
) {
  try {
    // Extract JSON from response if it's wrapped in markdown or other text
    const jsonMatch =
      response.match(/```json\n([\s\S]*?)\n```/) ||
      response.match(/```\n([\s\S]*?)\n```/);
    const jsonString = jsonMatch ? jsonMatch[1] : response;

    const parsed = JSON.parse(jsonString);

    // Try to parse with schema validation
    const result = schema.safeParse(parsed);

    if (!result.success) {
      console.warn(
        `⚠️ Schema validation failed for ${stepName}:`,
        result.error.issues
      );
      console.warn('Raw parsed data:', parsed);

      // Return the parsed data anyway, but log the validation issues
      return parsed;
    }

    return result.data;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error(`❌ Error parsing ${stepName} response:`, error);
    console.error('Raw response:', response);
    throw new Error(`Failed to parse ${stepName} response: ${error.message}`);
  }
}
