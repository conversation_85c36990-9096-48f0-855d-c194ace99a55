import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Loader2,
  FileText,
  Copy,
  RefreshCw,
  MessageSquare,
} from 'lucide-react';
import { TrendingTopic, WritingStyle, GeneratedPost } from '@/lib/storage';
import { ApiClient } from '@/lib/api';
import {
  getGeneratedPostsAction,
  saveGeneratedPostAction,
} from '@/lib/actions';

interface PostGeneratorProps {
  topics: TrendingTopic[];
  writingStyle: WritingStyle;
}

export function PostGenerator({ topics, writingStyle }: PostGeneratorProps) {
  const [selectedTopic, setSelectedTopic] = useState<TrendingTopic | null>(
    null
  );
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([]);
  const [generationOptions, setGenerationOptions] = useState({
    postType: 'story',
    lengthPreference: 'medium',
    includeHashtags: true,
    includeCallToAction: true,
    generateVariations: true,
    customInstructions: '',
  });

  useEffect(() => {
    const loadGeneratedPosts = async () => {
      try {
        const storedPosts = await getGeneratedPostsAction();
        if (storedPosts.length > 0) {
          setGeneratedPosts(storedPosts);
        }
      } catch (error) {
        console.error('Error loading generated posts:', error);
      }
    };

    loadGeneratedPosts();
  }, []);

  const handleGeneratePost = async () => {
    if (!selectedTopic) return;

    setIsGenerating(true);
    try {
      const post = await ApiClient.generatePost(
        selectedTopic,
        writingStyle,
        generationOptions
      );
      const newPosts = [...generatedPosts, post];
      setGeneratedPosts(newPosts);
      await saveGeneratedPostAction(post);
    } catch (error) {
      console.error('Error generating post:', error);
      alert('Error generating post. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  const regeneratePost = async (topic: TrendingTopic) => {
    setSelectedTopic(topic);
    await handleGeneratePost();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="w-5 h-5" />
            Select Topic
          </CardTitle>
          <CardDescription>
            Choose a trending topic to generate personalized posts.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {topics.map((topic, index) => (
              <div
                key={index}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedTopic?.title === topic.title
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedTopic(topic)}
              >
                <div className="space-y-2">
                  <h3 className="font-medium">{topic.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    {topic.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">{topic.category}</Badge>
                    <Badge variant="secondary">
                      {topic.engagement_potential}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {selectedTopic && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Generate Post
            </CardTitle>
            <CardDescription>
              Generate a personalized post based on the selected topic and your
              writing style.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium mb-2">{selectedTopic.title}</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  {selectedTopic.description}
                </p>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Content Angle: </span>
                    <span className="text-sm">
                      {selectedTopic.content_angle}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Post Ideas: </span>
                    <ul className="text-sm text-muted-foreground ml-4">
                      {selectedTopic.post_ideas.map((idea, idx) => (
                        <li key={idx}>• {idea}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Custom Instructions (optional)
                </label>
                <Textarea
                  placeholder="Add any specific instructions for the post generation..."
                  value={generationOptions.customInstructions}
                  onChange={(e) =>
                    setGenerationOptions((prev) => ({
                      ...prev,
                      customInstructions: e.target.value,
                    }))
                  }
                />
              </div>

              <Button
                onClick={handleGeneratePost}
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Generating Post...
                  </>
                ) : (
                  <>
                    <FileText className="w-4 h-4 mr-2" />
                    Generate Post
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {generatedPosts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Posts ({generatedPosts.length})</CardTitle>
            <CardDescription>
              Posts generated based on your selected topics and writing style.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {generatedPosts.map((post, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{post.post_type}</Badge>
                      <Badge variant="outline">
                        {post.estimated_engagement}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(post.post_content)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          regeneratePost(topics[index % topics.length])
                        }
                      >
                        <RefreshCw className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="whitespace-pre-wrap">{post.post_content}</p>
                    </div>

                    {post.hashtags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {post.hashtags.map((hashtag, idx) => (
                          <Badge
                            key={idx}
                            variant="secondary"
                            className="text-xs"
                          >
                            {hashtag}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {post.call_to_action && (
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <span className="text-sm font-medium">
                          Call to Action:{' '}
                        </span>
                        <span className="text-sm">{post.call_to_action}</span>
                      </div>
                    )}
                  </div>

                  {post.alternative_versions &&
                    post.alternative_versions.length > 0 && (
                      <div className="space-y-2">
                        <h4 className="font-medium">Alternative Versions:</h4>
                        <Tabs defaultValue="0" className="w-full">
                          <TabsList>
                            {post.alternative_versions.map((_, idx) => (
                              <TabsTrigger key={idx} value={idx.toString()}>
                                Version {idx + 1}
                              </TabsTrigger>
                            ))}
                          </TabsList>
                          {post.alternative_versions.map((version, idx) => (
                            <TabsContent key={idx} value={idx.toString()}>
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">
                                    {version.variation_type}
                                  </Badge>
                                  <Badge variant="secondary">
                                    {version.tone_adjustment}
                                  </Badge>
                                </div>
                                <div className="bg-gray-50 p-4 rounded-lg">
                                  <p className="whitespace-pre-wrap">
                                    {version.content}
                                  </p>
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    copyToClipboard(version.content)
                                  }
                                >
                                  <Copy className="w-4 h-4 mr-2" />
                                  Copy Version
                                </Button>
                              </div>
                            </TabsContent>
                          ))}
                        </Tabs>
                      </div>
                    )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
