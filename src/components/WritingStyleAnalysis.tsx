/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Loader2, User, Settings, FileText, Zap } from 'lucide-react';
import { WritingStyle } from '@/lib/storage';
import { ApiClient } from '@/lib/api';
import { saveWritingStyleAction, getWritingStyleAction } from '@/lib/actions';

interface WritingStyleAnalysisProps {
  onComplete: (writingStyle: WritingStyle) => void;
}

export function WritingStyleAnalysis({
  onComplete,
}: WritingStyleAnalysisProps) {
  const [facebookId, setFacebookId] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [writingStyle, setWritingStyle] = useState<WritingStyle | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedStyle, setEditedStyle] = useState<WritingStyle | null>(null);

  useEffect(() => {
    const loadWritingStyle = async () => {
      try {
        const storedStyle = await getWritingStyleAction();
        console.log(storedStyle);
        if (storedStyle) {
          setWritingStyle(storedStyle.data as any);
          setEditedStyle(storedStyle.data as any);
          setFacebookId(storedStyle.facebookUserId || '');
        }
      } catch (error) {
        console.error('Error loading writing style:', error);
      }
    };

    loadWritingStyle();
  }, []);

  const handleAnalyze = async () => {
    if (!facebookId.trim()) return;

    setIsAnalyzing(true);
    try {
      const style = await ApiClient.analyzeWritingStyle(facebookId);
      setWritingStyle(style);
      setEditedStyle(style);
    } catch (error) {
      console.error('Error analyzing writing style:', error);
      alert('Error analyzing writing style. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleSave = async () => {
    if (!editedStyle) return;

    try {
      await saveWritingStyleAction({
        ...editedStyle,
        facebookUserId: facebookId,
      });
      setWritingStyle(editedStyle);
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving writing style:', error);
      alert('Error saving writing style. Please try again.');
    }
  };

  const handleContinue = () => {
    if (writingStyle) {
      onComplete(writingStyle);
    }
  };

  const updateStyleField = (path: string, value: any) => {
    if (!editedStyle) return;

    const pathArray = path.split('.');
    const newStyle = { ...editedStyle };
    let current: any = newStyle;

    for (let i = 0; i < pathArray.length - 1; i++) {
      current = current[pathArray[i]];
    }

    current[pathArray[pathArray.length - 1]] = value;
    setEditedStyle(newStyle);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Writing Style Analysis
          </CardTitle>
          <CardDescription>
            Analyze your Facebook posts to understand your unique writing style
            and voice.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="facebook-id">Facebook Profile ID</Label>
              <div className="flex gap-2">
                <Input
                  id="facebook-id"
                  placeholder="Enter your Facebook profile ID"
                  value={facebookId}
                  onChange={(e) => setFacebookId(e.target.value)}
                  disabled={isAnalyzing}
                />
                <Button
                  onClick={handleAnalyze}
                  disabled={!facebookId.trim() || isAnalyzing}
                >
                  {isAnalyzing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Analyze
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {writingStyle && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Your Writing Style Profile
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(!isEditing)}
              >
                <Settings className="w-4 h-4 mr-2" />
                {isEditing ? 'Cancel' : 'Edit'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="tone">Tone</TabsTrigger>
                <TabsTrigger value="structure">Structure</TabsTrigger>
                <TabsTrigger value="engagement">Engagement</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Primary Tone</Label>
                    {isEditing ? (
                      <Input
                        value={editedStyle?.writingStyle?.tone.primary || ''}
                        onChange={(e) =>
                          updateStyleField('tone.primary', e.target.value)
                        }
                      />
                    ) : (
                      <Badge variant="secondary" className="capitalize">
                        {writingStyle.writingStyle.tone.primary}
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Formality Level</Label>
                    {isEditing ? (
                      <Input
                        value={
                          editedStyle?.writingStyle.tone.formality_level || ''
                        }
                        onChange={(e) =>
                          updateStyleField(
                            'tone.formality_level',
                            e.target.value
                          )
                        }
                      />
                    ) : (
                      <Badge variant="outline" className="capitalize">
                        {writingStyle?.writingStyle.tone.formality_level.replace(
                          '_',
                          ' '
                        )}
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Humor Style</Label>
                    {isEditing ? (
                      <Input
                        value={
                          editedStyle?.writingStyle.content_patterns
                            .humor_style || ''
                        }
                        onChange={(e) =>
                          updateStyleField(
                            'content_patterns.humor_style',
                            e.target.value
                          )
                        }
                      />
                    ) : (
                      <Badge variant="secondary" className="capitalize">
                        {writingStyle.writingStyle.content_patterns.humor_style}
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Storytelling Style</Label>
                    {isEditing ? (
                      <Input
                        value={
                          editedStyle?.writingStyle.content_patterns
                            .storytelling_style || ''
                        }
                        onChange={(e) =>
                          updateStyleField(
                            'content_patterns.storytelling_style',
                            e.target.value
                          )
                        }
                      />
                    ) : (
                      <Badge variant="outline" className="capitalize">
                        {
                          writingStyle.writingStyle.content_patterns
                            .storytelling_style
                        }
                      </Badge>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="tone" className="space-y-4">
                <div className="space-y-2">
                  <Label>Repetition Patterns</Label>
                  <div className="flex flex-wrap gap-2">
                    {writingStyle.writingStyle.linguistic_features.repetition_patterns.map(
                      (item, index) => (
                        <Badge key={index} variant="secondary">
                          {item}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Transition Words</Label>
                  <div className="flex flex-wrap gap-2">
                    {writingStyle.writingStyle.linguistic_features.transition_words.map(
                      (item, index) => (
                        <Badge key={index} variant="outline">
                          {item}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Emphasis Techniques</Label>
                  <div className="flex flex-wrap gap-2">
                    {writingStyle.writingStyle.linguistic_features.emphasis_techniques.map(
                      (item, index) => (
                        <Badge key={index} variant="secondary">
                          {item}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Rhetorical Devices</Label>
                  <div className="flex flex-wrap gap-2">
                    {writingStyle.writingStyle.linguistic_features.rhetorical_devices.map(
                      (item, index) => (
                        <Badge key={index} variant="secondary">
                          {item}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Cultural References</Label>
                  <div className="flex flex-wrap gap-2">
                    {writingStyle.writingStyle.linguistic_features.cultural_references.map(
                      (item, index) => (
                        <Badge key={index} variant="outline">
                          {item}
                        </Badge>
                      )
                    )}
                  </div>
                </div>
                <Accordion type="single" collapsible>
                  <AccordionItem value="emotional-range">
                    <AccordionTrigger>Emotional Range</AccordionTrigger>
                    <AccordionContent>
                      <div className="flex flex-wrap gap-2">
                        {writingStyle.writingStyle.tone.emotional_range.map(
                          (emotion, index) => (
                            <Badge key={index} variant="secondary">
                              {emotion}
                            </Badge>
                          )
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </TabsContent>

              <TabsContent value="structure" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Sentence Length</Label>
                    <Badge variant="outline" className="capitalize">
                      {writingStyle.writingStyle.sentence_structure.average_length.replace(
                        '_',
                        ' '
                      )}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <Label>Paragraph Length</Label>
                    <Badge variant="outline" className="capitalize">
                      {writingStyle.writingStyle.paragraph_structure.typical_length.replace(
                        '_',
                        ' '
                      )}
                    </Badge>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="engagement" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Hashtag Usage</Label>
                    <Badge variant="secondary" className="capitalize">
                      {
                        writingStyle.writingStyle.engagement_patterns
                          .hashtag_usage
                      }
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <Label>Emoji Usage</Label>
                    <Badge variant="secondary" className="capitalize">
                      {
                        writingStyle.writingStyle.engagement_patterns
                          .emoji_usage
                      }
                    </Badge>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {isEditing && (
              <div className="flex gap-2 mt-4">
                <Button onClick={handleSave}>Save Changes</Button>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
              </div>
            )}

            {!isEditing && (
              <Button onClick={handleContinue} className="w-full mt-4">
                Continue to Trending Topics
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
