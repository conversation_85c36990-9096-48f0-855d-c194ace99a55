import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, TrendingUp, Users, Settings, Plus, X } from 'lucide-react';
import { TrendingTopic, UserPreferences } from '@/lib/storage';
import { ApiClient } from '@/lib/api';
import {
  getTrendingTopicsAction,
  saveTrendingTopicsAction,
  getCompetitorIdsAction,
  saveCompetitorIdsAction,
  getUserPreferencesAction,
  saveUserPreferencesAction,
} from '@/lib/actions';
import { useSession } from 'next-auth/react';
interface TrendingTopicsGeneratorProps {
  onComplete: (topics: TrendingTopic[]) => void;
}

export function TrendingTopicsGenerator({
  onComplete,
}: TrendingTopicsGeneratorProps) {
  const [competitorIds, setCompetitorIds] = useState<string[]>([]);
  const [newCompetitorId, setNewCompetitorId] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);
  const [userPreferences, setUserPreferences] = useState<UserPreferences>({
    interests: [],
    target_audience: [],
    content_style: '',
    language_preference: '',
    engagement_goals: '',
    avoid_topics: [],
    preferred_categories: [],
    tone_preference: '',
  });
  const { data: session } = useSession();

  const userId = session?.user?.id;

  const loadData = async () => {
    try {
      const [storedTopics, storedCompetitorIds, storedPreferences] =
        await Promise.all([
          getTrendingTopicsAction(),
          getCompetitorIdsAction(),
          getUserPreferencesAction(),
        ]);

      if (storedTopics.length > 0) {
        setTrendingTopics(storedTopics);
      }

      if (storedCompetitorIds.length > 0) {
        setCompetitorIds(storedCompetitorIds);
      }

      if (storedPreferences) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        setUserPreferences(storedPreferences.data as any);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const addCompetitorId = async () => {
    if (
      newCompetitorId.trim() &&
      !competitorIds.includes(newCompetitorId.trim())
    ) {
      const newIds = [...competitorIds, newCompetitorId.trim()];
      setCompetitorIds(newIds);
      try {
        await saveCompetitorIdsAction(newIds);
      } catch (error) {
        console.error('Error saving competitor IDs:', error);
      }
      setNewCompetitorId('');
    }
  };

  const removeCompetitorId = async (id: string) => {
    const newIds = competitorIds.filter((cid) => cid !== id);
    setCompetitorIds(newIds);
    try {
      await saveCompetitorIdsAction(newIds);
    } catch (error) {
      console.error('Error saving competitor IDs:', error);
    }
  };

  const arePreferencesValid = (): boolean => {
    const {
      interests,
      target_audience,
      content_style,
      language_preference,
      engagement_goals,
      avoid_topics,
      preferred_categories,
      tone_preference,
    } = userPreferences;

    return (
      interests.length > 0 &&
      target_audience.length > 0 &&
      content_style.trim() !== '' &&
      language_preference.trim() !== '' &&
      engagement_goals.trim() !== '' &&
      avoid_topics.length > 0 &&
      preferred_categories.length > 0 &&
      tone_preference.trim() !== ''
    );
  };

  const handleGenerate = async () => {
    if (competitorIds.length === 0 || !arePreferencesValid()) return;

    setIsGenerating(true);
    try {
      const topics = await ApiClient.generateTrendingTopics(
        competitorIds,
        userPreferences
      );

      await Promise.all([
        saveTrendingTopicsAction(
          topics.map((topic) => ({
            userId: userId as string,
            title: topic.title,
            description: topic.description as string,
            category: topic.category,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            data: topic as any,
          }))
        ),
        saveUserPreferencesAction(userPreferences),
      ]);
      await loadData();
    } catch (error) {
      console.error('Error generating trending topics:', error);
      alert('Error generating trending topics. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleContinue = () => {
    if (trendingTopics.length > 0) {
      onComplete(trendingTopics);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updatePreferences = (key: keyof UserPreferences, value: any) => {
    setUserPreferences((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const updateArrayPreference = (
    key: keyof UserPreferences,
    value: string,
    remove = false
  ) => {
    setUserPreferences((prev) => ({
      ...prev,
      [key]: remove
        ? (prev[key] as string[]).filter((item) => item !== value)
        : [...(prev[key] as string[]), value],
    }));
  };

  return (
    <div className="space-y-6">
      {/* Competitor Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Competitor Analysis
          </CardTitle>
          <CardDescription>
            Add Facebook profile IDs of competitors or influencers to analyze
            their trending topics.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Input
                placeholder="Enter Facebook profile ID"
                value={newCompetitorId}
                onChange={(e) => setNewCompetitorId(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && addCompetitorId()}
              />
              <Button onClick={addCompetitorId}>
                <Plus className="w-4 h-4 mr-2" />
                Add
              </Button>
            </div>

            <div className="space-y-2">
              <Label>Competitor IDs ({competitorIds.length})</Label>
              <div className="flex flex-wrap gap-2 min-h-[2rem] p-2 border rounded-md bg-muted/30">
                {competitorIds.map((id) => (
                  <Badge
                    key={id}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {id}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-4 h-4 p-0 hover:bg-transparent"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        removeCompetitorId(id);
                      }}
                    >
                      <X className="w-3 h-3 cursor-pointer hover:text-destructive" />
                    </Button>
                  </Badge>
                ))}
                {competitorIds.length === 0 && (
                  <span className="text-muted-foreground text-sm">
                    No competitor IDs added yet
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Content Preferences
          </CardTitle>
          <CardDescription>
            Fill all preferences to generate relevant trending topics.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="interests" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="interests">Interests</TabsTrigger>
              <TabsTrigger value="audience">Audience</TabsTrigger>
              <TabsTrigger value="style">Style</TabsTrigger>
              <TabsTrigger value="categories">Categories</TabsTrigger>
            </TabsList>

            <TabsContent value="interests" className="space-y-4">
              <Label>Your Interests</Label>
              <div className="flex flex-wrap gap-2 min-h-[2rem] p-2 border rounded-md bg-muted/30">
                {userPreferences.interests.map((interest) => (
                  <Badge
                    key={interest}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {interest}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-4 h-4 p-0 hover:bg-transparent"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        updateArrayPreference('interests', interest, true);
                      }}
                    >
                      <X className="w-3 h-3 cursor-pointer hover:text-destructive" />
                    </Button>
                  </Badge>
                ))}
                {userPreferences.interests.length === 0 && (
                  <span className="text-muted-foreground text-sm">
                    No interests added yet
                  </span>
                )}
              </div>
              <Input
                placeholder="Type an interest and press Enter to add"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    const value = (e.target as HTMLInputElement).value.trim();
                    if (value && !userPreferences.interests.includes(value)) {
                      updateArrayPreference('interests', value);
                      (e.target as HTMLInputElement).value = '';
                    }
                  }
                }}
              />
            </TabsContent>

            <TabsContent value="audience" className="space-y-4">
              <Label>Target Audience</Label>
              <div className="flex flex-wrap gap-2 min-h-[2rem] p-2 border rounded-md bg-muted/30">
                {userPreferences.target_audience.map((audience) => (
                  <Badge
                    key={audience}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {audience}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-4 h-4 p-0 hover:bg-transparent"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        updateArrayPreference(
                          'target_audience',
                          audience,
                          true
                        );
                      }}
                    >
                      <X className="w-3 h-3 cursor-pointer hover:text-destructive" />
                    </Button>
                  </Badge>
                ))}
                {userPreferences.target_audience.length === 0 && (
                  <span className="text-muted-foreground text-sm">
                    No audience added yet
                  </span>
                )}
              </div>
              <Input
                placeholder="Type target audience and press Enter to add"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    const value = (e.target as HTMLInputElement).value.trim();
                    if (
                      value &&
                      !userPreferences.target_audience.includes(value)
                    ) {
                      updateArrayPreference('target_audience', value);
                      (e.target as HTMLInputElement).value = '';
                    }
                  }
                }}
              />
            </TabsContent>

            <TabsContent value="style" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Content Style</Label>
                  <Input
                    value={userPreferences.content_style}
                    onChange={(e) =>
                      updatePreferences('content_style', e.target.value)
                    }
                    placeholder="e.g., informative, humorous"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Tone Preference</Label>
                  <Input
                    value={userPreferences.tone_preference}
                    onChange={(e) =>
                      updatePreferences('tone_preference', e.target.value)
                    }
                    placeholder="e.g., friendly, formal"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Language Preference</Label>
                  <Input
                    value={userPreferences.language_preference}
                    onChange={(e) =>
                      updatePreferences('language_preference', e.target.value)
                    }
                    placeholder="e.g., English, Spanish, mixed"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Engagement Goals</Label>
                  <Input
                    value={userPreferences.engagement_goals}
                    onChange={(e) =>
                      updatePreferences('engagement_goals', e.target.value)
                    }
                    placeholder="e.g., inform, entertain, convert"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Avoid Topics</Label>
                  <div className="flex flex-wrap gap-2 min-h-[2rem] p-2 border rounded-md bg-muted/30">
                    {userPreferences.avoid_topics.map((topic) => (
                      <Badge
                        key={topic}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {topic}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-4 h-4 p-0 hover:bg-transparent"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            updateArrayPreference('avoid_topics', topic, true);
                          }}
                        >
                          <X className="w-3 h-3 cursor-pointer hover:text-destructive" />
                        </Button>
                      </Badge>
                    ))}
                    {userPreferences.avoid_topics.length === 0 && (
                      <span className="text-muted-foreground text-sm">
                        No topics to avoid added yet
                      </span>
                    )}
                  </div>
                  <Input
                    placeholder="Add topic to avoid"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        updateArrayPreference(
                          'avoid_topics',
                          (e.target as HTMLInputElement).value
                        );
                        (e.target as HTMLInputElement).value = '';
                      }
                    }}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="categories" className="space-y-4">
              <Label>Preferred Categories</Label>
              <div className="flex flex-wrap gap-2 min-h-[2rem] p-2 border rounded-md bg-muted/30">
                {userPreferences.preferred_categories.map((category) => (
                  <Badge
                    key={category}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {category}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-4 h-4 p-0 hover:bg-transparent"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        updateArrayPreference(
                          'preferred_categories',
                          category,
                          true
                        );
                      }}
                    >
                      <X className="w-3 h-3 cursor-pointer hover:text-destructive" />
                    </Button>
                  </Badge>
                ))}
                {userPreferences.preferred_categories.length === 0 && (
                  <span className="text-muted-foreground text-sm">
                    No categories added yet
                  </span>
                )}
              </div>
              <Input
                placeholder="Add category"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    updateArrayPreference(
                      'preferred_categories',
                      (e.target as HTMLInputElement).value
                    );
                    (e.target as HTMLInputElement).value = '';
                  }
                }}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Generate Button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Generate Trending Topics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button
            onClick={handleGenerate}
            disabled={
              competitorIds.length === 0 ||
              isGenerating ||
              !arePreferencesValid()
            }
            className="w-full"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating Topics...
              </>
            ) : (
              <>
                <TrendingUp className="w-4 h-4 mr-2" />
                Generate Trending Topics
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results */}
      {trendingTopics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Trending Topics ({trendingTopics.length})</CardTitle>
            <CardDescription>
              Topics generated based on competitor analysis and your
              preferences.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trendingTopics.map((topic, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{topic.title}</h3>
                    <Badge variant="secondary">
                      {Math.round(topic.relevance_score * 100)}% relevance
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {topic.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">{topic.category}</Badge>
                    <Badge variant="outline">
                      {topic.engagement_potential}
                    </Badge>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {topic.hashtags.slice(0, 3).map((hashtag, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs">
                        {hashtag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <Button onClick={handleContinue} className="w-full mt-4">
              Continue to Post Generation
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
