'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { StepIndicator } from '@/components/StepIndicator';
import { WritingStyleAnalysis } from '@/components/WritingStyleAnalysis';
import { TrendingTopicsGenerator } from '@/components/TrendingTopicsGenerator';
import { PostGenerator } from '@/components/PostGenerator';
import { TrendingTopic, WritingStyle } from '@/lib/storage';
import { BarChart3, TrendingUp, Sparkles } from 'lucide-react';
import { AuthGuard } from '@/components/AuthGuard';
import { UserMenu } from '@/components/UserMenu';
import { SessionProvider } from 'next-auth/react';

export default function HomePage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [writingStyle, setWritingStyle] = useState<WritingStyle | null>(null);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);

  const stepLabels = ['Writing Style', 'Trending Topics', 'Post Generation'];

  const handleWritingStyleComplete = (style: WritingStyle) => {
    setWritingStyle(style);
    setCurrentStep(2);
  };

  const handleTrendingTopicsComplete = (topics: TrendingTopic[]) => {
    setTrendingTopics(topics);
    setCurrentStep(3);
  };

  const resetToStep = (step: number) => {
    setCurrentStep(step);
  };

  return (
    <SessionProvider>
      <AuthGuard>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-between items-start mb-8">
              <div className="text-center flex-1">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  Social Media Content Analyzer
                </h1>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  AI-powered platform to analyze your writing style, discover
                  trending topics, and generate personalized social media
                  content that resonates with your audience.
                </p>
              </div>
              <UserMenu />
            </div>

            <StepIndicator
              currentStep={currentStep}
              totalSteps={3}
              stepLabels={stepLabels}
            />

            <div className="space-y-6">
              {currentStep === 1 && (
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <Card className="text-center">
                      <CardHeader className="pb-3">
                        <BarChart3 className="w-8 h-8 mx-auto text-blue-500" />
                        <CardTitle className="text-lg">Analyze</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription>
                          Understand your unique writing style and voice
                          patterns
                        </CardDescription>
                      </CardContent>
                    </Card>
                    <Card className="text-center">
                      <CardHeader className="pb-3">
                        <TrendingUp className="w-8 h-8 mx-auto text-green-500" />
                        <CardTitle className="text-lg">Discover</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription>
                          Find trending topics from competitor analysis
                        </CardDescription>
                      </CardContent>
                    </Card>
                    <Card className="text-center">
                      <CardHeader className="pb-3">
                        <Sparkles className="w-8 h-8 mx-auto text-purple-500" />
                        <CardTitle className="text-lg">Generate</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription>
                          Create personalized posts that match your style
                        </CardDescription>
                      </CardContent>
                    </Card>
                  </div>
                  <WritingStyleAnalysis
                    onComplete={handleWritingStyleComplete}
                  />
                </div>
              )}

              {currentStep === 2 && (
                <TrendingTopicsGenerator
                  onComplete={handleTrendingTopicsComplete}
                />
              )}

              {currentStep === 3 && writingStyle && (
                <PostGenerator
                  topics={trendingTopics}
                  writingStyle={writingStyle}
                />
              )}
            </div>

            <div className="mt-12 text-center">
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => resetToStep(1)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Back to Writing Style
                </button>
                {currentStep >= 2 && (
                  <button
                    onClick={() => resetToStep(2)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Back to Trending Topics
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </AuthGuard>
    </SessionProvider>
  );
}
