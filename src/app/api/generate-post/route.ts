import { NextRequest, NextResponse } from 'next/server';
import { ChatMistralAI } from '@langchain/mistralai';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';
import env from '@/lib/env';
import { parseJSONResponse } from '@/lib/utils';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { FacebookPostSchema } from '@/lib/zod';

const model = new ChatMistralAI({
  model: 'mistral-medium',
  temperature: 0.7,
  apiKey: env.MISTRAL_API_KEY,
});

const postGenerationPrompt = PromptTemplate.fromTemplate(`
You are an expert social media content creator who specializes in generating Facebook posts that perfectly match a user's writing style and voice. Generate a Facebook post based on the provided topic and writing style analysis.

CRITICAL INSTRUCTION: You MUST return ONLY valid JSON. Do not include any explanatory text, markdown formatting, or code blocks. Start your response with {{ and end with }}.

Topic to Write About:
Title: {topic_title}
Description: {topic_description}
Category: {topic_category}
Content Angle: {content_angle}
Post Ideas: {post_ideas}
Suggested Hashtags: {suggested_hashtags}
Target Audience: {target_audience}
Cultural Context: {cultural_context}

Writing Style Analysis:
{writing_style_analysis}

Post Generation Requirements:
- Post Type: {post_type}
- Length Preference: {length_preference}
- Include Hashtags: {include_hashtags}
- Include Call to Action: {include_call_to_action}
- Generate Variations: {generate_variations}
- Custom Instructions: {custom_instructions}

Based on the writing style analysis, create a Facebook post that:
1. Matches the user's tone (conversational, reflective, humorous)
2. Uses their vocabulary preferences and common words
3. Follows their sentence and paragraph structure patterns
4. Incorporates their engagement patterns and audience addressing style
5. Includes their storytelling style and personal anecdotes approach
6. Uses appropriate cultural references and linguistic features
7. Maintains their humor style and emphasis techniques

Return a JSON object with the following structure:

{{
  "post_content": "The main Facebook post content that matches the user's writing style perfectly",
  "hashtags": ["#hashtag1", "#hashtag2", "#hashtag3"],
  "engagement_hooks": ["Hook 1", "Hook 2", "Hook 3"],
  "call_to_action": "Optional call to action text",
  "post_type": "story",
  "estimated_engagement": "high",
  "best_posting_time": "Best time suggestion based on topic and audience",
  "target_audience_alignment": 0.95,
  "cultural_relevance": 0.9,
  "style_adherence": 0.95,
  "alternative_versions": [
    {{
      "content": "Alternative version 1",
      "variation_type": "shorter version",
      "tone_adjustment": "more casual"
    }},
    {{
      "content": "Alternative version 2",
      "variation_type": "longer version",
      "tone_adjustment": "more detailed"
    }}
  ]
}}
`);

const postGenerationChain = RunnableSequence.from([
  postGenerationPrompt,
  model,
  (output) =>
    parseJSONResponse(
      output.content,
      FacebookPostSchema,
      'Generate Post Content Based On Topic'
    ),
]);

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.name) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { topic, writingStyle, options = {} } = await request.json();

    if (!topic || !writingStyle) {
      return NextResponse.json(
        { error: 'Topic and writing style are required' },
        { status: 400 }
      );
    }

    const generatedPost = await postGenerationChain.invoke({
      topic_title: topic.title,
      topic_description: topic.description,
      topic_category: topic.category,
      content_angle: topic.content_angle,
      post_ideas: topic.post_ideas.join(', '),
      suggested_hashtags: topic.hashtags.join(', '),
      target_audience: topic.target_audience.join(', '),
      cultural_context: topic.cultural_context || '',
      writing_style_analysis: JSON.stringify(writingStyle, null, 2),
      post_type: options.postType || 'story',
      length_preference: options.lengthPreference || 'medium',
      include_hashtags: options.includeHashtags !== false,
      include_call_to_action: options.includeCallToAction !== false,
      generate_variations: options.generateVariations !== false,
      custom_instructions: options.customInstructions || 'None',
    });

    return NextResponse.json(generatedPost);
  } catch (error) {
    console.error('Error generating post:', error);
    return NextResponse.json(
      { error: 'Failed to generate post' },
      { status: 500 }
    );
  }
}
