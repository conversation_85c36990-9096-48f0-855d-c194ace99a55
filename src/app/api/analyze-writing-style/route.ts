import { NextRequest, NextResponse } from 'next/server';
import { ChatMistralAI } from '@langchain/mistralai';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { parseJSONResponse } from '@/lib/utils';
import { StyleAnalysisSchema } from '@/lib/zod';
import env from '@/lib/env';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { DatabaseService } from '@/lib/database';

const model = new ChatMistralAI({
  model: 'mistral-medium',
  temperature: 0.1,
  apiKey: env.MISTRAL_API_KEY,
});

const analysisPrompt = PromptTemplate.fromTemplate(`
You are an expert writing style analyst. Analyze the following Facebook posts and extract a comprehensive writing style profile.

CRITICAL INSTRUCTION: You MUST return ONLY valid JSON. Do not include any explanatory text, markdown formatting, or code blocks. Start your response with {{ and end with }}.

Facebook Posts to Analyze:
{posts}

Please analyze the writing style deeply and provide a detailed profile. You MUST return ONLY valid JSON in the exact format below:

{{
  "writingStyle": {{
    "tone": {{
      "primary": "describe the primary tone",
      "secondary": "describe the secondary tone",
      "emotional_range": ["emotion1", "emotion2", "emotion3"],
      "formality_level": "one of: very_formal, formal, neutral, informal, very_informal"
    }},
    "vocabulary": {{
      "complexity_level": "one of: basic, intermediate, advanced, expert",
      "common_words": ["word1", "word2", "word3"],
      "technical_terms": ["term1", "term2"],
      "slang_colloquialisms": ["slang1", "slang2"],
      "preferred_adjectives": ["adj1", "adj2"],
      "preferred_verbs": ["verb1", "verb2"]
    }},
    "sentence_structure": {{
      "average_length": "one of: very_short, short, medium, long, very_long",
      "complexity": "one of: simple, compound, complex, compound_complex",
      "preferred_starters": ["starter1", "starter2"],
      "punctuation_style": ["style1", "style2"],
      "question_frequency": "one of: never, rarely, sometimes, often, frequently"
    }},
    "paragraph_structure": {{
      "typical_length": "one of: single_sentence, short, medium, long",
      "opening_style": ["style1", "style2"],
      "closing_style": ["style1", "style2"],
      "flow_patterns": ["pattern1", "pattern2"]
    }},
    "engagement_patterns": {{
      "call_to_action_style": ["cta1", "cta2"],
      "hashtag_usage": "one of: none, minimal, moderate, heavy",
      "emoji_usage": "one of: none, minimal, moderate, heavy",
      "personal_pronouns": ["I", "you", "we"],
      "audience_addressing": "one of: direct, indirect, mixed"
    }},
    "content_patterns": {{
      "storytelling_style": "one of: narrative, descriptive, analytical, persuasive, mixed",
      "topic_introduction": ["intro1", "intro2"],
      "examples_usage": "one of: none, minimal, moderate, frequent",
      "personal_anecdotes": "one of: none, minimal, moderate, frequent",
      "humor_style": "one of: none, subtle, obvious, sarcastic, playful"
    }},
    "linguistic_features": {{
      "repetition_patterns": ["pattern1", "pattern2"],
      "transition_words": ["word1", "word2"],
      "emphasis_techniques": ["technique1", "technique2"],
      "rhetorical_devices": ["device1", "device2"],
      "cultural_references": ["ref1", "ref2"]
    }}
  }},
  "generationGuidelines": {{
    "template_structure": "describe the template structure",
    "key_principles": ["principle1", "principle2"],
    "do_list": ["do1", "do2"],
    "dont_list": ["dont1", "dont2"],
    "adaptation_notes": ["note1", "note2"]
  }},
  "examples": {{
    "representative_excerpts": ["excerpt1", "excerpt2"],
    "style_variations": ["variation1", "variation2"]
  }},
  "styleNotes": {{
    "tone_summary": "describe the tone",
    "stylistic_signatures": ["signature1", "signature2"],
    "target_audience": "describe the target audience",
    "platform_behavior": "describe the platform behavior",
    "overall_impression": "describe the overall impression and analysis on writing style",
  }},
  "metadata": {{
    "posts_analyzed": 0,
    "total_word_count": 0,
    "average_post_length": 0,
    "confidence_score": 0.0
  }},
  "style_notes": ["note1", "note2"],
}}

Return ONLY the JSON object, no additional text or explanation.
`);

const analysisChain = RunnableSequence.from([
  analysisPrompt,
  model,
  (output) =>
    parseJSONResponse(
      output.content,
      StyleAnalysisSchema,
      'writing style analysis'
    ),
]);

async function fetchFacebookPosts(facebookId: string) {
  const facebookPosts = await fetch(
    `${env.FACEBOOK_POST_SCRAPER_URL}/facebook/profile`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: facebookId,
        maxPosts: 5,
      }),
    }
  ).then((res) => res.json());

  return facebookPosts.data.posts;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.name) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { facebookId } = await request.json();

    if (!facebookId) {
      return NextResponse.json(
        { error: 'Facebook ID is required' },
        { status: 400 }
      );
    }

    const posts = await fetchFacebookPosts(facebookId);

    if (posts.length === 0) {
      return NextResponse.json(
        { error: 'No posts found for the given Facebook ID' },
        { status: 400 }
      );
    }

    const mergedPosts = posts
      .map((post: { postText: string }) => post.postText)
      .join('\n----\n');

    const writingStyle = await analysisChain.invoke({
      posts: mergedPosts,
    });

    // Save to database instead of returning directly
    await DatabaseService.saveWritingStyle(session.user.id, {
      data: writingStyle,
      facebookUserId: facebookId,
    });

    return NextResponse.json({ writingStyle });
  } catch (error) {
    console.error('Error analyzing writing style:', error);
    return NextResponse.json(
      { error: 'Failed to analyze writing style' },
      { status: 500 }
    );
  }
}
