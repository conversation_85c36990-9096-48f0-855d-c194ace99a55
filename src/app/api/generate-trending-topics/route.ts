import { NextRequest, NextResponse } from 'next/server';
import { ChatMistralAI } from '@langchain/mistralai';
import { PromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';
import { z } from 'zod';
import env from '@/lib/env';
import { parseJSONResponse } from '@/lib/utils';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { TrendingTopicsAnalysisSchema } from '@/lib/zod';

const model = new ChatMistralAI({
  model: 'mistral-small-latest',
  temperature: 0.3,
  apiKey: env.MISTRAL_API_KEY,
});

const trendingTopicsPrompt = PromptTemplate.fromTemplate(`
You are an expert social media trend analyst and content strategist. Analyze the following Facebook posts and generate trending topics that would be relevant and engaging for the user based on their preferences.

CRITICAL INSTRUCTION: You MUST return ONLY valid JSON. Do not include any explanatory text, markdown formatting, or code blocks. Start your response with {{ and end with }}.

Facebook Posts to Analyze:
{posts}

Based on the analysis, generate 5-8 trending topics that would resonate with the user and their audience. Consider:
1. Current themes and patterns in the posts
2. User's interests and content style preferences
3. Cultural context and language preferences
4. Engagement potential and audience alignment
5. Timing and relevance factors

Return a JSON object with the following structure:

{{
  "trending_topics": [
    {{
      "title": "Topic title that captures attention",
      "description": "Brief description of the topic and why it's trending",
      "category": "technology",
      "relevance_score": 0.95,
      "hashtags": ["#hashtag1", "#hashtag2", "#hashtag3"],
      "target_audience": ["tech enthusiasts", "young professionals"],
      "engagement_potential": "high",
      "content_angle": "How to approach this topic from user's perspective",
      "post_ideas": [
        "Specific post idea 1",
        "Specific post idea 2",
        "Specific post idea 3"
      ],
      "timing_suggestion": "Best time to post about this topic",
      "cultural_context": "Cultural relevance and context"
    }}
  ]
}}
`);

const trendingTopicsChain = RunnableSequence.from([
  trendingTopicsPrompt,
  model,
  (output) =>
    parseJSONResponse(
      output.content,
      TrendingTopicsAnalysisSchema,
      'Generate trending topics'
    ),
]);

async function fetchCompetitorPosts(competitorIds: string[]) {
  const posts = [];

  for (const id of competitorIds) {
    const facebookPosts = await fetch(
      `${env.FACEBOOK_POST_SCRAPER_URL}/facebook/profile`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: id,
          maxPosts: 5,
        }),
      }
    ).then((res) => res.json());

    posts.push(...(facebookPosts?.data?.posts || []));
  }

  return posts;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.name) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { competitorIds } = await request.json();

    if (
      !competitorIds ||
      !Array.isArray(competitorIds) ||
      competitorIds.length === 0
    ) {
      return NextResponse.json(
        { error: 'Competitor IDs are required' },
        { status: 400 }
      );
    }

    const posts = await fetchCompetitorPosts(competitorIds);
    const mergedPosts = posts.map((post) => post.postText).join('\n----\n');

    const result = await trendingTopicsChain.invoke({
      posts: mergedPosts,
      //   user_preferences: JSON.stringify(userPreferences, null, 2),
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error generating trending topics:', error);
    return NextResponse.json(
      { error: 'Failed to generate trending topics' },
      { status: 500 }
    );
  }
}
