generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql" // or "sqlite" for development
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  facebookId    String?   @unique
  accounts      Account[]
  sessions      Session[]

  writingStyle    WritingStyle?
  trendingTopics  TrendingTopic[]
  generatedPosts  GeneratedPost[]
  userPreferences UserPreferences?
  competitorIds   String[]
}

model WritingStyle {
  id             String  @id @default(cuid())
  userId         String  @unique
  data           Json
  facebookUserId String?
  user           User    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model TrendingTopic {
  id          String   @id @default(cuid())
  userId      String
  title       String
  description String
  category    String
  data        Json
  createdAt   DateTime @default(now())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model GeneratedPost {
  id        String   @id @default(cuid())
  userId    String
  content   String   @db.Text
  data      Json
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model UserPreferences {
  id     String @id @default(cuid())
  userId String @unique
  data   Json
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
}
